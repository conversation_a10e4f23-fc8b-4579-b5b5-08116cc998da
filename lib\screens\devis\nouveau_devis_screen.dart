import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/devis_provider.dart';
import '../../providers/firebase_client_provider.dart';
import '../../providers/produit_provider.dart';
import '../../models/devis.dart';
import '../../models/produit.dart';
import '../../models/client.dart';
import '../../models/devis_item.dart';
import '../../widgets/professional_ui_components.dart';
import '../../widgets/vitabrosse_logo.dart';

class NouveauDevisScreen extends StatefulWidget {
  final Devis? devisAModifier;

  const NouveauDevisScreen({super.key, this.devisAModifier});

  @override
  State<NouveauDevisScreen> createState() => _NouveauDevisScreenState();
}

class _NouveauDevisScreenState extends State<NouveauDevisScreen> {
  final TextEditingController _conditionsController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final TextEditingController _contactController = TextEditingController();
  final TextEditingController _remiseController = TextEditingController();
  final TextEditingController _tvaController = TextEditingController(
    text: '19',
  );

  List<DevisItem> _items = [];
  Client? _clientSelectionne;
  DateTime _dateExpiration = DateTime.now().add(const Duration(days: 30));
  bool _remiseEnPourcentage = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    print('DEBUG: NouveauDevisScreen initState called');
    print('DEBUG: Widget mounted: $mounted');

    try {
      _loadData();
      _initializeEditMode();
      print('DEBUG: NouveauDevisScreen initialization completed');
    } catch (e) {
      print('DEBUG ERROR: Error in NouveauDevisScreen initState: $e');
    }
  }

  void _initializeEditMode() {
    if (widget.devisAModifier != null) {
      final devis = widget.devisAModifier!;
      // Note: We'll need to fetch the client by ID
      _dateExpiration = devis.dateExpiration;
      _items = List.from(devis.items);
      _conditionsController.text = devis.conditionsValidite;
      _notesController.text = devis.notes ?? '';
      _contactController.text = devis.contactCommercial ?? '';
      _remiseController.text = devis.remisePourcentage > 0
          ? devis.remisePourcentage.toString()
          : devis.remiseMontant.toString();
      _tvaController.text = devis.tauxTva.toString();
      _remiseEnPourcentage = devis.remisePourcentage > 0;

      // Load client by ID
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadClientById(devis.clientId);
      });
    }
  }

  Future<void> _loadData() async {
    print('DEBUG: _loadData() called - Starting data loading');

    try {
      print('DEBUG: Getting providers...');
      final clientProvider = context.read<FirebaseClientProvider>();
      final produitProvider = context.read<ProduitProvider>();

      print('DEBUG: Providers obtained successfully');
      print(
          'DEBUG: Current products count: ${produitProvider.produits.length}');
      print('DEBUG: Current clients count: ${clientProvider.clients.length}');

      print('DEBUG: Starting Future.wait for data loading...');
      await Future.wait([
        clientProvider.chargerClients(),
        if (produitProvider.produits.isEmpty) produitProvider.chargerProduits(),
      ]);

      print('DEBUG: Data loading completed successfully');
      print('DEBUG: Final products count: ${produitProvider.produits.length}');
      print('DEBUG: Final clients count: ${clientProvider.clients.length}');
    } catch (e) {
      print('DEBUG ERROR: Failed to load data: $e');
      print('DEBUG ERROR: Stack trace: ${StackTrace.current}');
    }
  }

  Future<void> _loadClientById(String clientId) async {
    final clientProvider = context.read<FirebaseClientProvider>();
    final client = clientProvider.clients.firstWhere(
      (c) => c.id == clientId,
      orElse: () => throw Exception('Client not found'),
    );
    setState(() {
      _clientSelectionne = client;
    });
  }

  @override
  void dispose() {
    _conditionsController.dispose();
    _notesController.dispose();
    _contactController.dispose();
    _remiseController.dispose();
    _tvaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 768;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 1,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: const Color(0xFF1F2937)),
              onPressed: () => Navigator.of(context).pop(),
            ),
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(
                left: isSmallScreen ? 56.0 : 60.0, // Add space for back button
                right: isSmallScreen ? 16.0 : 20.0,
                bottom: 16,
              ),
              title: Row(
                children: [
                  const VitaBrosseLogo(height: 28, showText: false),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          widget.devisAModifier != null
                              ? 'Modifier Devis'
                              : 'Nouveau Devis',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize: isSmallScreen ? 18 : 22,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          widget.devisAModifier != null
                              ? 'Modifier la proposition'
                              : 'nouvelle proposition',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF3B82F6).withValues(alpha: 0.05),
                      const Color(0xFF1D4ED8).withValues(alpha: 0.05),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (isSmallScreen) ...[
                    _buildClientSection(isSmallScreen),
                    SizedBox(height: 16),
                    _buildDevisInfo(isSmallScreen),
                    SizedBox(height: 16),
                    _buildArticlesSection(isSmallScreen),
                    SizedBox(height: 16),
                    _buildFinancialSettings(isSmallScreen),
                    SizedBox(height: 16),
                    _buildNotesSection(isSmallScreen),
                  ] else ...[
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          flex: 2,
                          child: Column(
                            children: [
                              _buildClientSection(isSmallScreen),
                              SizedBox(height: 24),
                              _buildDevisInfo(isSmallScreen),
                            ],
                          ),
                        ),
                        SizedBox(width: 24),
                        Expanded(
                          flex: 3,
                          child: Column(
                            children: [
                              _buildArticlesSection(isSmallScreen),
                              SizedBox(height: 24),
                              _buildFinancialSettings(isSmallScreen),
                              SizedBox(height: 24),
                              _buildNotesSection(isSmallScreen),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                  SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    child: PrimaryActionButton(
                      text: widget.devisAModifier != null
                          ? 'Modifier le devis'
                          : 'Enregistrer le devis',
                      icon: widget.devisAModifier != null
                          ? Icons.edit
                          : Icons.save,
                      onPressed: _enregistrerDevis,
                      isLoading: _isLoading,
                      isFullWidth: true,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClientSection(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Informations Client',
            style: TextStyle(
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          SizedBox(height: 16),
          Consumer<FirebaseClientProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DropdownButtonFormField<Client>(
                    value: _clientSelectionne,
                    decoration: InputDecoration(
                      labelText: 'Sélectionner un client',
                      prefixIcon: Icon(
                        Icons.person,
                        color: const Color(0xFF3B82F6),
                      ),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    items: provider.clients.map<DropdownMenuItem<Client>>((
                      Client client,
                    ) {
                      return DropdownMenuItem<Client>(
                        value: client,
                        child: Text(client.nomComplet),
                      );
                    }).toList(),
                    onChanged: (Client? client) {
                      setState(() {
                        _clientSelectionne = client;
                      });
                    },
                  ),
                  if (_clientSelectionne != null) ...[
                    SizedBox(height: 16),
                    Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFF3B82F6).withValues(alpha: 0.05),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: const Color(0xFF3B82F6).withValues(alpha: 0.2),
                        ),
                      ),
                      child: Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: const Color(0xFF3B82F6),
                            child: Text(
                              _clientSelectionne!.nomComplet
                                  .substring(0, 1)
                                  .toUpperCase(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _clientSelectionne!.nomComplet,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: isSmallScreen ? 14 : 16,
                                  ),
                                ),
                                if (_clientSelectionne!.email.isNotEmpty)
                                  _buildClientInfoRow(
                                    'Email',
                                    _clientSelectionne!.email,
                                    Icons.email,
                                  ),
                                if (_clientSelectionne!.telephone != null &&
                                    _clientSelectionne!.telephone!.isNotEmpty)
                                  _buildClientInfoRow(
                                    'Téléphone',
                                    _clientSelectionne!.telephone!,
                                    Icons.phone,
                                  ),
                                _buildClientInfoRow(
                                  'Adresse',
                                  _clientSelectionne!.adresse,
                                  Icons.location_on,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildClientInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: EdgeInsets.only(top: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDevisInfo(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Informations du Devis',
            style: TextStyle(
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Date d\'expiration',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[700],
                        fontSize: isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: 8),
                    InkWell(
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _dateExpiration,
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now().add(
                            const Duration(days: 365),
                          ),
                        );
                        if (date != null) {
                          setState(() {
                            _dateExpiration = date;
                          });
                        }
                      },
                      child: Container(
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.calendar_today,
                              color: const Color(0xFF7C3AED),
                              size: isSmallScreen ? 16 : 18,
                            ),
                            SizedBox(width: 8),
                            Text(
                              '${_dateExpiration.day.toString().padLeft(2, '0')}/'
                              '${_dateExpiration.month.toString().padLeft(2, '0')}/'
                              '${_dateExpiration.year}',
                              style: TextStyle(
                                color: const Color(0xFF1F2937),
                                fontWeight: FontWeight.w500,
                                fontSize: isSmallScreen ? 14 : 16,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Contact commercial',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[700],
                        fontSize: isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: 8),
                    TextField(
                      controller: _contactController,
                      style: TextStyle(fontSize: isSmallScreen ? 14 : 16),
                      decoration: InputDecoration(
                        hintText: 'Nom du commercial',
                        hintStyle: TextStyle(
                          color: Colors.grey[500],
                          fontSize: isSmallScreen ? 14 : 16,
                        ),
                        prefixIcon: Icon(
                          Icons.person_outline,
                          color: const Color(0xFF7C3AED),
                          size: isSmallScreen ? 16 : 18,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          TextField(
            controller: _conditionsController,
            decoration: InputDecoration(
              labelText: 'Conditions de validité',
              hintText: 'Ex: Offre valable 30 jours...',
              prefixIcon: Icon(
                Icons.assignment,
                color: const Color(0xFF059669),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  Widget _buildArticlesSection(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Articles',
                style: TextStyle(
                  fontSize: isSmallScreen ? 16 : 18,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1F2937),
                ),
              ),
              OutlinedButton.icon(
                onPressed: () {
                  print(
                      'DEBUG: Main Ajouter button pressed - About to call _ajouterArticle()');
                  try {
                    _ajouterArticle();
                  } catch (e) {
                    print('DEBUG ERROR: Exception in main button press: $e');
                    print('DEBUG ERROR: Stack trace: ${StackTrace.current}');
                  }
                },
                icon: const Icon(Icons.add, size: 18),
                label: const Text('Ajouter'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: const Color(0xFF3B82F6),
                  side: const BorderSide(color: Color(0xFF3B82F6)),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (_items.isEmpty)
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.inventory_2_outlined,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Aucun article ajouté',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Commencez par ajouter des articles à votre devis',
                    style: TextStyle(color: Colors.grey[500], fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          else
            Column(
              children: _items.asMap().entries.map((entry) {
                return _buildArticleItem(entry.value, entry.key);
              }).toList(),
            ),
          if (_items.isNotEmpty) ...[
            SizedBox(height: 16),
            _buildFinancialSummary(),
          ],
        ],
      ),
    );
  }

  Widget _buildArticleItem(DevisItem item, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.designation,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  'Ref: ${item.reference}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            flex: 1,
            child: Text(
              '${item.quantite} ${item.unite}',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            flex: 1,
            child: Text(
              item.prixUnitaireFormate,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            flex: 1,
            child: Text(
              item.sousTotalFormate,
              style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(width: 4),
          IconButton(
            onPressed: () => _supprimerArticle(index),
            icon: const Icon(Icons.delete, color: Colors.red, size: 18),
            constraints: BoxConstraints(minWidth: 32, minHeight: 32),
            padding: EdgeInsets.all(4),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummary() {
    // Calculate values
    final sousTotal = _items.fold(0.0, (sum, item) => sum + item.sousTotal);
    final remiseValue = double.tryParse(_remiseController.text) ?? 0.0;
    final remise =
        _remiseEnPourcentage ? (sousTotal * remiseValue / 100) : remiseValue;
    final totalHT = sousTotal - remise;
    final tauxTva = double.tryParse(_tvaController.text) ?? 19.0;
    final montantTva = totalHT * tauxTva / 100;
    final totalTTC = totalHT + montantTva;

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF3B82F6).withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF3B82F6).withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          // Sous-total
          _buildSummaryRow(
            'Sous-total HT:',
            '${sousTotal.toStringAsFixed(3)} DT',
            isSubtitle: true,
          ),

          // Remise (if applicable)
          if (remise > 0) ...[
            SizedBox(height: 8),
            _buildSummaryRow(
              'Remise ${_remiseEnPourcentage ? "($remiseValue%)" : ""}:',
              '- ${remise.toStringAsFixed(3)} DT',
              color: const Color(0xFFF59E0B),
              isSubtitle: true,
            ),
          ],

          // Total HT
          if (remise > 0) ...[
            SizedBox(height: 8),
            _buildSummaryRow(
              'Total HT:',
              '${totalHT.toStringAsFixed(3)} DT',
              isSubtitle: true,
            ),
          ],

          // TVA
          SizedBox(height: 8),
          _buildSummaryRow(
            'TVA ($tauxTva%):',
            '${montantTva.toStringAsFixed(3)} DT',
            color: Colors.grey[600],
            isSubtitle: true,
          ),

          // Separator
          SizedBox(height: 12),
          Container(
            height: 1,
            color: const Color(0xFF3B82F6).withValues(alpha: 0.3),
          ),
          SizedBox(height: 12),

          // Total TTC
          _buildSummaryRow(
            'Total TTC:',
            '${totalTTC.toStringAsFixed(3)} DT',
            color: const Color(0xFF3B82F6),
            isBold: true,
            fontSize: 18,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    String value, {
    Color? color,
    bool isBold = false,
    bool isSubtitle = false,
    double? fontSize,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: fontSize ?? (isSubtitle ? 14 : 16),
            fontWeight: isBold
                ? FontWeight.w700
                : (isSubtitle ? FontWeight.w500 : FontWeight.w600),
            color: color ?? const Color(0xFF1F2937),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: fontSize ?? (isSubtitle ? 14 : 16),
            fontWeight: isBold ? FontWeight.w700 : FontWeight.w600,
            color: color ?? const Color(0xFF1F2937),
          ),
        ),
      ],
    );
  }

  Widget _buildFinancialSettings(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,gnment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF59E0B).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.calculate_outlined,
                  color: const Color(0xFFF59E0B),
                  size: isSmallScreen ? 20 : 24,
                ),
              ),
              SizedBox(width: 12),
              Text(
                'Paramètres Financiers',
                style: TextStyle(
                  fontSize: isSmallScreen ? 16 : 18,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF1F2937),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          isSmallScreen
              ? Column(
                  children: [
                    // Remise Section for small screens
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[200]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.discount_outlined,
                                color: const Color(0xFFF59E0B),
                                size: 16,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Remise',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF1F2937),
                                ),
                              ),
                              Spacer(),
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: _remiseEnPourcentage
                                      ? const Color(
                                          0xFFF59E0B,
                                        ).withValues(alpha: 0.1)
                                      : Colors.grey[200],
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Transform.scale(
                                      scale: 0.8,
                                      child: Switch(
                                        value: _remiseEnPourcentage,
                                        onChanged: (value) {
                                          setState(() {
                                            _remiseEnPourcentage = value;
                                            _remiseController.clear();
                                          });
                                        },
                                        activeColor: const Color(0xFFF59E0B),
                                        materialTapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                      ),
                                    ),
                                    Text(
                                      _remiseEnPourcentage ? '%' : 'DT',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 12,
                                        color: _remiseEnPourcentage
                                            ? const Color(0xFFF59E0B)
                                            : Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 12),
                          TextField(
                            controller: _remiseController,
                            style: TextStyle(fontSize: 14),
                            decoration: InputDecoration(
                              hintText: _remiseEnPourcentage
                                  ? 'Pourcentage de remise'
                                  : 'Montant fixe en DT',
                              hintStyle: TextStyle(
                                color: Colors.grey[500],
                                fontSize: 14,
                              ),
                              prefixIcon: Icon(
                                _remiseEnPourcentage
                                    ? Icons.percent
                                    : Icons.attach_money,
                                color: const Color(0xFFF59E0B),
                                size: 16,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: const Color(0xFFF59E0B),
                                  width: 2,
                                ),
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              isDense: true,
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (_) => setState(() {}),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 16),
                    // TVA Section for small screens
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey[200]!),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.percent,
                                color: const Color(0xFFF59E0B),
                                size: 16,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'TVA (%)',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF1F2937),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 12),
                          TextField(
                            controller: _tvaController,
                            style: TextStyle(fontSize: 14),
                            decoration: InputDecoration(
                              hintText: 'Taux de TVA en %',
                              hintStyle: TextStyle(
                                color: Colors.grey[500],
                                fontSize: 14,
                              ),
                              prefixIcon: Icon(
                                Icons.percent,
                                color: const Color(0xFFF59E0B),
                                size: 16,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide:
                                    BorderSide(color: Colors.grey[300]!),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide(
                                  color: const Color(0xFFF59E0B),
                                  width: 2,
                                ),
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              isDense: true,
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (_) => setState(() {}),
                          ),
                        ],
                      ),
                    ),
                  ],
                )
              : Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[200]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.discount_outlined,
                                  color: const Color(0xFFF59E0B),
                                  size: 18,
                                ),
                                SizedBox(width: 8),
                                Flexible(
                                  child: Text(
                                    'Remise',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF1F2937),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 8),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _remiseEnPourcentage
                                        ? const Color(
                                            0xFFF59E0B,
                                          ).withValues(alpha: 0.1)
                                        : Colors.grey[200],
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Transform.scale(
                                        scale: 0.8,
                                        child: Switch(
                                          value: _remiseEnPourcentage,
                                          onChanged: (value) {
                                            setState(() {
                                              _remiseEnPourcentage = value;
                                              _remiseController.clear();
                                            });
                                          },
                                          activeColor: const Color(0xFFF59E0B),
                                          materialTapTargetSize:
                                              MaterialTapTargetSize.shrinkWrap,
                                        ),
                                      ),
                                      Text(
                                        _remiseEnPourcentage ? '%' : 'DT',
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                          fontSize: 12,
                                          color: _remiseEnPourcentage
                                              ? const Color(0xFFF59E0B)
                                              : Colors.grey[600],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 12),
                            TextField(
                              controller: _remiseController,
                              style: TextStyle(fontSize: 16),
                              decoration: InputDecoration(
                                hintText: _remiseEnPourcentage
                                    ? 'Pourcentage de remise'
                                    : 'Montant fixe en DT',
                                hintStyle: TextStyle(
                                  color: Colors.grey[500],
                                  fontSize: 16,
                                ),
                                prefixIcon: Icon(
                                  _remiseEnPourcentage
                                      ? Icons.percent
                                      : Icons.attach_money,
                                  color: const Color(0xFFF59E0B),
                                  size: 18,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: Colors.grey[300]!,
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: Colors.grey[300]!,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: const Color(0xFFF59E0B),
                                    width: 2,
                                  ),
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              keyboardType: TextInputType.number,
                              onChanged: (_) => setState(() {}),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[200]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.percent,
                                  color: const Color(0xFFF59E0B),
                                  size: 18,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  'TVA (%)',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF1F2937),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 12),
                            TextField(
                              controller: _tvaController,
                              style: TextStyle(fontSize: 16),
                              decoration: InputDecoration(
                                hintText: 'Taux de TVA en %',
                                hintStyle: TextStyle(
                                  color: Colors.grey[500],
                                  fontSize: 16,
                                ),
                                prefixIcon: Icon(
                                  Icons.percent,
                                  color: const Color(0xFFF59E0B),
                                  size: 18,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: Colors.grey[300]!,
                                  ),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: Colors.grey[300]!,
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  borderSide: BorderSide(
                                    color: const Color(0xFFF59E0B),
                                    width: 2,
                                  ),
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 12,
                                ),
                              ),
                              keyboardType: TextInputType.number,
                              onChanged: (_) => setState(() {}),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  Widget _buildNotesSection(bool isSmallScreen) {
    return ProfessionalCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Notes',
            style: TextStyle(
              fontSize: isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _notesController,
            decoration: const InputDecoration(
              hintText: 'Ajouter des notes pour ce devis...',
              border: OutlineInputBorder(),
            ),
            maxLines: 4,
          ),
        ],
      ),
    );
  }

  void _ajouterArticle() {
    print('DEBUG: _ajouterArticle() called');
    _afficherModalSelectionProduit();
  }

  void _afficherModalSelectionProduit() {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: screenHeight * 0.85,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Header avec poignée de fermeture
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF10B981).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.inventory_2_outlined,
                          color: Color(0xFF10B981),
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Sélectionner un produit',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: const Color(0xFF1F2937),
                              ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.grey.shade100,
                          foregroundColor: Colors.grey.shade600,
                          padding: const EdgeInsets.all(8),
                          minimumSize: const Size(36, 36),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Liste des produits
            Expanded(
              child: Consumer<ProduitProvider>(
                builder: (context, provider, child) {
                  return ListView.builder(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 16 : 24,
                      vertical: 8,
                    ),
                    itemCount: provider.produits.length,
                    itemBuilder: (context, index) {
                      final produit = provider.produits[index];
                      final isAvailable = produit.estDisponible;

                      return Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isAvailable
                                ? Colors.grey.shade200
                                : Colors.red.shade200,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.04),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Row(
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: isAvailable
                                      ? const Color(0xFF10B981).withValues(alpha: 0.1)
                                      : Colors.red.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  isAvailable
                                      ? Icons.inventory_2_outlined
                                      : Icons.warning_outlined,
                                  color: isAvailable
                                      ? const Color(0xFF10B981)
                                      : Colors.red.shade600,
                                  size: 24,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      produit.nom,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: isAvailable
                                            ? const Color(0xFF1F2937)
                                            : Colors.grey.shade600,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Code: ${produit.code}',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Row(
                                      children: [
                                        Text(
                                          produit.prixFormate,
                                          style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                            color: isAvailable
                                                ? const Color(0xFF10B981)
                                                : Colors.grey.shade500,
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Text(
                                          'Stock: ${produit.stock}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: isAvailable
                                                ? Colors.grey.shade600
                                                : Colors.red.shade600,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 12),
                              isAvailable
                                  ? FilledButton(
                                      onPressed: () => _ajouterProduit(produit),
                                      style: FilledButton.styleFrom(
                                        backgroundColor: const Color(0xFF10B981),
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 8,
                                        ),
                                        minimumSize: const Size(0, 36),
                                      ),
                                      child: const Text(
                                        'Ajouter',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    )
                                  : Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 6,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.red.shade50,
                                        borderRadius: BorderRadius.circular(6),
                                        border: Border.all(
                                          color: Colors.red.shade200,
                                        ),
                                      ),
                                      child: Text(
                                        'Rupture',
                                        style: TextStyle(
                                          color: Colors.red.shade600,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _ajouterProduit(Produit produit) {
    Navigator.pop(context);
    _afficherDialogueQuantite(produit);
  }

  void _afficherDialogueQuantite(Produit produit) {
    final quantiteController = TextEditingController(text: '1');
    final prixController = TextEditingController(text: produit.prix.toString());
    final remiseController = TextEditingController(text: '0');
    bool remiseEnPourcentage = true;
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.add_shopping_cart,
                  color: Color(0xFF10B981),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Ajouter ${produit.nom}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF1F2937),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          content: SizedBox(
            width: isSmallScreen ? double.maxFinite : 400,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product info
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              produit.nom,
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            Text(
                              'Code: ${produit.code}',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                              ),
                            ),
                            Text(
                              'Stock disponible: ${produit.stock}',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Quantity
                TextField(
                  controller: quantiteController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Quantité',
                    prefixIcon: const Icon(Icons.numbers, color: Color(0xFF3B82F6)),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Prix unitaire
                TextField(
                  controller: prixController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Prix unitaire (DT)',
                    prefixIcon: const Icon(Icons.attach_money, color: Color(0xFF10B981)),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Remise
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: TextField(
                        controller: remiseController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          labelText: 'Remise',
                          prefixIcon: const Icon(Icons.discount, color: Color(0xFFF59E0B)),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          setDialogState(() {
                            remiseEnPourcentage = !remiseEnPourcentage;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          decoration: BoxDecoration(
                            color: remiseEnPourcentage
                                ? const Color(0xFFF59E0B).withValues(alpha: 0.1)
                                : Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: remiseEnPourcentage
                                  ? const Color(0xFFF59E0B)
                                  : Colors.grey.shade300,
                            ),
                          ),
                          child: Text(
                            remiseEnPourcentage ? '%' : 'DT',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color: remiseEnPourcentage
                                  ? const Color(0xFFF59E0B)
                                  : Colors.grey.shade600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            FilledButton(
              onPressed: () {
                final quantite = int.tryParse(quantiteController.text) ?? 0;
                final prix = double.tryParse(prixController.text) ?? 0.0;
                final remise = double.tryParse(remiseController.text) ?? 0.0;

                if (quantite > 0 && prix > 0 && quantite <= produit.stock) {
                  Navigator.pop(context);
                  _ajouterItem(produit, quantite, prix, remise, remiseEnPourcentage);
                } else if (quantite > produit.stock) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Quantité demandée supérieure au stock disponible (${produit.stock})'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              style: FilledButton.styleFrom(
                backgroundColor: const Color(0xFF10B981),
              ),
              child: const Text('Ajouter'),
            ),
          ],
        ),
      ),
    );
  }

  void _ajouterItem(Produit produit, int quantite, double prix, double remise, bool remiseEnPourcentage) {
    // Calculate discount
    final sousTotal = prix * quantite;
    final montantRemise = remiseEnPourcentage ? (sousTotal * remise / 100) : remise;
    final prixFinal = prix - (remiseEnPourcentage ? (prix * remise / 100) : (remise / quantite));

    // Check if product already exists in the list
    final index = _items.indexWhere((item) => item.produitId == produit.id);

    if (index >= 0) {
      // Update existing item
      setState(() {
        final existingItem = _items[index];
        _items[index] = DevisItem(
          produitId: produit.id!,
          reference: produit.code,
          designation: produit.nom,
          quantite: existingItem.quantite + quantite,
          unite: 'pièce',
          prixUnitaireHT: prixFinal,
        );
      });
    } else {
      // Add new item
      final item = DevisItem(
        produitId: produit.id!,
        reference: produit.code,
        designation: produit.nom,
        quantite: quantite,
        unite: 'pièce',
        prixUnitaireHT: prixFinal,
      );

      setState(() {
        _items.add(item);
      });
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${produit.nom} ajouté au devis'),
        backgroundColor: const Color(0xFF10B981),
      ),
    );
  }

  void _supprimerArticle(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  void _enregistrerDevis() async {
    if (_clientSelectionne == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez sélectionner un client')),
      );
      return;
    }

    if (_items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez ajouter au moins un article')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final devis = Devis(
        id: widget.devisAModifier?.id, // Keep existing ID if editing
        numero: widget.devisAModifier?.numero ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        clientId: _clientSelectionne!.id!,
        dateCreation: widget.devisAModifier?.dateCreation ?? DateTime.now(),
        dateExpiration: _dateExpiration,
        conditionsValidite: _conditionsController.text,
        items: _items,
        remisePourcentage: _remiseEnPourcentage
            ? (double.tryParse(_remiseController.text) ?? 0.0)
            : 0.0,
        remiseMontant: !_remiseEnPourcentage
            ? (double.tryParse(_remiseController.text) ?? 0.0)
            : 0.0,
        tauxTva: double.tryParse(_tvaController.text) ?? 19.0,
        notes: _notesController.text,
        contactCommercial:
            _contactController.text.isEmpty ? null : _contactController.text,
      );

      bool success;
      String message;

      if (widget.devisAModifier != null) {
        // Mode modification
        success = await context.read<DevisProvider>().modifierDevis(devis);
        message = success
            ? 'Devis modifié avec succès'
            : 'Erreur lors de la modification';
      } else {
        // Mode création
        success = await context.read<DevisProvider>().creerDevis(devis);
        message =
            success ? 'Devis créé avec succès' : 'Erreur lors de la création';
      }

      if (success && mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(message), backgroundColor: Colors.green),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
    _prixController.dispose();
    _remiseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print('DEBUG: _ArticleSelectionDialogState build method called');

    try {
      final screenSize = MediaQuery.of(context).size;
      final isSmallScreen = screenSize.width < 600;
      print(
          'DEBUG: Screen size calculated - width: ${screenSize.width}, isSmallScreen: $isSmallScreen');

      print('DEBUG: About to create Dialog widget');
      final dialog = Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: isSmallScreen ? screenSize.width * 0.95 : 500,
          constraints: BoxConstraints(
            maxHeight: screenSize.height * 0.6, // Reduced from 0.85 to 0.6
            maxWidth: isSmallScreen ? screenSize.width * 0.95 : 500,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Compact Header
              Builder(builder: (context) {
                print('DEBUG: Creating header container');
                return Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ), // Reduced padding
                  decoration: BoxDecoration(
                    color: const Color(0xFF3B82F6),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16),
                      topRight: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.add_shopping_cart,
                        color: Colors.white,
                        size: 18,
                      ), // Smaller icon
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Ajouter un article',
                          style: TextStyle(
                            fontSize: 14, // Smaller font
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: Icon(Icons.close, color: Colors.white, size: 18),
                        constraints:
                            BoxConstraints(minWidth: 28, minHeight: 28),
                        padding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                );
              }),

              // Compact Content
              Builder(builder: (context) {
                print('DEBUG: Creating expanded content section');
                return Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(16), // Reduced padding
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Product Selection
                        Builder(builder: (context) {
                          print('DEBUG: Creating "Produit" text widget');
                          return Text(
                            'Produit',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF1F2937),
                            ),
                          );
                        }),
                        Builder(builder: (context) {
                          print('DEBUG: Creating SizedBox after Produit text');
                          return SizedBox(height: 6);
                        }),
                        Builder(builder: (context) {
                          print(
                              'DEBUG: About to create Consumer<ProduitProvider>');
                          return Consumer<ProduitProvider>(
                            builder: (context, provider, child) {
                              print(
                                  'DEBUG: Consumer<ProduitProvider> builder called');
                              print(
                                  'DEBUG: Provider isLoading: ${provider.isLoading}');
                              print(
                                  'DEBUG: Provider products count: ${provider.produits.length}');
                              print('DEBUG: Provider error: ${provider.error}');

                              if (provider.isLoading) {
                                print(
                                    'DEBUG: Showing loading indicator for products');
                                return Container(
                                  height: 48,
                                  decoration: BoxDecoration(
                                    border:
                                        Border.all(color: Colors.grey[300]!),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Center(
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2),
                                  ),
                                );
                              }

                              print(
                                  'DEBUG: Creating _ProductSearchField with ${provider.produits.length} products');
                              return _ProductSearchField(
                                products: provider.produits,
                                selectedProduct: _produitSelectionne,
                                onProductSelected: (produit) {
                                  print(
                                      'DEBUG: Product selected: ${produit?.nom ?? "null"}');
                                  try {
                                    setState(() {
                                      _produitSelectionne = produit;
                                      _prixController.text =
                                          produit?.prix.toString() ?? '';
                                      print(
                                          'DEBUG: Product selection state updated successfully');
                                    });
                                  } catch (e) {
                                    print(
                                        'DEBUG ERROR: Failed to update product selection: $e');
                                  }
                                },
                              );
                            },
                          );
                        }),

                        SizedBox(height: 12), // Reduced spacing
                        // Quantity and Price
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Quantité',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF1F2937),
                                    ),
                                  ),
                                  SizedBox(height: 4),
                                  TextField(
                                    controller: _quantiteController,
                                    decoration: InputDecoration(
                                      hintText: '1',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: 10,
                                        vertical: 6,
                                      ),
                                      isDense: true,
                                    ),
                                    keyboardType: TextInputType.number,
                                    onChanged: (_) => setState(() {}),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Prix unitaire (DT)',
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                      color: const Color(0xFF1F2937),
                                    ),
                                  ),
                                  SizedBox(height: 4),
                                  TextField(
                                    controller: _prixController,
                                    decoration: InputDecoration(
                                      hintText: '0.00',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: 10,
                                        vertical: 6,
                                      ),
                                      isDense: true,
                                    ),
                                    keyboardType: TextInputType.number,
                                    onChanged: (_) => setState(() {}),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),

                        SizedBox(height: 12), // Reduced spacing
                        // Remise Section
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        'Remise',
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                          color: const Color(0xFF1F2937),
                                        ),
                                      ),
                                      Spacer(),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 6,
                                          vertical: 2,
                                        ),
                                        decoration: BoxDecoration(
                                          color: _remiseEnPourcentage
                                              ? const Color(
                                                  0xFFF59E0B,
                                                ).withValues(alpha: 0.1)
                                              : Colors.grey[200],
                                          borderRadius:
                                              BorderRadius.circular(8),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Transform.scale(
                                              scale: 0.6,
                                              child: Switch(
                                                value: _remiseEnPourcentage,
                                                onChanged: (value) {
                                                  setState(() {
                                                    _remiseEnPourcentage =
                                                        value;
                                                    _remiseController.clear();
                                                  });
                                                },
                                                activeColor: const Color(
                                                  0xFFF59E0B,
                                                ),
                                                materialTapTargetSize:
                                                    MaterialTapTargetSize
                                                        .shrinkWrap,
                                              ),
                                            ),
                                            Text(
                                              _remiseEnPourcentage ? '%' : 'DT',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w600,
                                                fontSize: 10,
                                                color: _remiseEnPourcentage
                                                    ? const Color(0xFFF59E0B)
                                                    : Colors.grey[600],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 4),
                                  TextField(
                                    controller: _remiseController,
                                    decoration: InputDecoration(
                                      hintText: _remiseEnPourcentage
                                          ? '0%'
                                          : '0.00 DT',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: 10,
                                        vertical: 6,
                                      ),
                                      isDense: true,
                                      prefixIcon: Icon(
                                        _remiseEnPourcentage
                                            ? Icons.percent
                                            : Icons.money_off,
                                        size: 14,
                                        color: const Color(0xFFF59E0B),
                                      ),
                                    ),
                                    keyboardType: TextInputType.number,
                                    onChanged: (_) => setState(() {}),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),

                        // Total preview
                        if (_quantiteController.text.isNotEmpty &&
                            _prixController.text.isNotEmpty) ...[
                          SizedBox(height: 12),
                          Container(
                            padding: EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: const Color(0xFF3B82F6)
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: const Color(
                                  0xFF3B82F6,
                                ).withValues(alpha: 0.3),
                              ),
                            ),
                            child: Column(
                              children: [
                                // Subtotal
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Sous-total:',
                                      style: TextStyle(
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    Text(
                                      '${((int.tryParse(_quantiteController.text) ?? 0) * (double.tryParse(_prixController.text) ?? 0)).toStringAsFixed(3)} DT',
                                      style: TextStyle(
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                                // Discount if applicable
                                if (_remiseController.text.isNotEmpty &&
                                    double.tryParse(_remiseController.text) !=
                                        null &&
                                    double.tryParse(_remiseController.text)! >
                                        0) ...[
                                  SizedBox(height: 4),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Remise${_remiseEnPourcentage ? " (${_remiseController.text}%)" : ""}:',
                                        style: TextStyle(
                                          fontSize: 10,
                                          color: const Color(0xFFF59E0B),
                                        ),
                                      ),
                                      Text(
                                        () {
                                          final subtotal = (int.tryParse(
                                                    _quantiteController.text,
                                                  ) ??
                                                  0) *
                                              (double.tryParse(
                                                    _prixController.text,
                                                  ) ??
                                                  0);
                                          final remiseValue = double.tryParse(
                                                _remiseController.text,
                                              ) ??
                                              0.0;
                                          final discount = _remiseEnPourcentage
                                              ? (subtotal * remiseValue / 100)
                                              : remiseValue;
                                          return '- ${discount.toStringAsFixed(3)} DT';
                                        }(),
                                        style: TextStyle(
                                          fontSize: 10,
                                          color: const Color(0xFFF59E0B),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: 4),
                                  Container(height: 1, color: Colors.grey[300]),
                                  SizedBox(height: 4),
                                ],
                                // Final total
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Total:',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    Text(
                                      () {
                                        final subtotal = (int.tryParse(
                                                  _quantiteController.text,
                                                ) ??
                                                0) *
                                            (double.tryParse(
                                                  _prixController.text,
                                                ) ??
                                                0);
                                        final remiseValue = double.tryParse(
                                              _remiseController.text,
                                            ) ??
                                            0.0;
                                        final discount = _remiseEnPourcentage
                                            ? (subtotal * remiseValue / 100)
                                            : remiseValue;
                                        final total = subtotal - discount;
                                        return '${total.toStringAsFixed(3)} DT';
                                      }(),
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w700,
                                        color: const Color(0xFF3B82F6),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                );
              }),

              // Footer
              Container(
                padding: EdgeInsets.all(12), // Reduced padding
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(16),
                    bottomRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            vertical: 8,
                          ), // Reduced padding
                        ),
                        child: Text('Annuler', style: TextStyle(fontSize: 12)),
                      ),
                    ),
                    SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _produitSelectionne != null &&
                                _quantiteController.text.isNotEmpty &&
                                _prixController.text.isNotEmpty
                            ? () {
                                print('DEBUG: Dialog Ajouter button pressed');
                                print(
                                    'DEBUG: Selected product: ${_produitSelectionne?.nom}');
                                print(
                                    'DEBUG: Quantity: ${_quantiteController.text}');
                                print('DEBUG: Price: ${_prixController.text}');
                                _ajouterArticleToDialog();
                              }
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF3B82F6),
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(
                            vertical: 8,
                          ), // Reduced padding
                        ),
                        child: Text('Ajouter', style: TextStyle(fontSize: 12)),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );

      print('DEBUG: Dialog widget created successfully');
      return dialog;
    } catch (e) {
      print('DEBUG ERROR: Error building dialog: $e');
      return Dialog(
        child: Container(
          padding: EdgeInsets.all(20),
          child: Text('Error loading dialog: $e'),
        ),
      );
    }
  }

  void _ajouterArticleToDialog() {
    print('DEBUG: _ajouterArticleToDialog called');

    try {
      final quantite = int.tryParse(_quantiteController.text) ?? 1;
      final prix = double.tryParse(_prixController.text) ?? 0.0;
      final remiseValue = double.tryParse(_remiseController.text) ?? 0.0;

      print(
          'DEBUG: Parsed values - quantite: $quantite, prix: $prix, remise: $remiseValue');

      // Calculate discounted price
      final discount = _remiseEnPourcentage
          ? (prix * remiseValue / 100)
          : (remiseValue / quantite);
      final prixFinal = prix - discount;

      if (_produitSelectionne != null && quantite > 0 && prixFinal > 0) {
        final item = DevisItem(
          produitId: _produitSelectionne!.id!,
          reference: _produitSelectionne!.code,
          designation: _produitSelectionne!.nom,
          quantite: quantite,
          unite:
              'pièce', // Valeur par défaut car le modèle Produit n'a pas de champ unite
          prixUnitaireHT: prixFinal,
        );

        print('DEBUG: Creating DevisItem and calling onArticleSelected');
        widget.onArticleSelected(item);
        print('DEBUG: onArticleSelected completed, closing dialog');
        Navigator.of(context).pop();
      }
    } catch (e) {
      print('DEBUG ERROR: Error in _ajouterArticleToDialog: $e');
    }
  }
}

class _ProductSearchField extends StatefulWidget {
  final List<Produit> products;
  final Produit? selectedProduct;
  final Function(Produit?) onProductSelected;

  const _ProductSearchField({
    required this.products,
    required this.selectedProduct,
    required this.onProductSelected,
  });

  @override
  State<_ProductSearchField> createState() => _ProductSearchFieldState();
}

class _ProductSearchFieldState extends State<_ProductSearchField> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<Produit> _filteredProducts = [];
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    print('DEBUG: _ProductSearchField initState called');
    print('DEBUG: Products received: ${widget.products.length}');
    print('DEBUG: Selected product: ${widget.selectedProduct?.nom ?? "null"}');

    try {
      _filteredProducts = widget.products;
      if (widget.selectedProduct != null) {
        _searchController.text = widget.selectedProduct!.nom;
        print(
            'DEBUG: Search controller text set to: ${_searchController.text}');
      }
      _focusNode.addListener(_onFocusChanged);
      print('DEBUG: _ProductSearchField initialization completed successfully');
    } catch (e) {
      print('DEBUG ERROR: Error in _ProductSearchField initState: $e');
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _onFocusChanged() {
    if (_focusNode.hasFocus) {
      _showSearchDropdown();
    } else {
      Future.delayed(Duration(milliseconds: 150), () {
        _hideSearchDropdown();
      });
    }
  }

  void _filterProducts(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredProducts = widget.products;
      } else {
        _filteredProducts = widget.products.where((product) {
          return product.nom.toLowerCase().contains(query.toLowerCase()) ||
              product.code.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
    _updateOverlay();
  }

  void _showSearchDropdown() {
    if (_overlayEntry != null) return;

    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _hideSearchDropdown() {
    _removeOverlay();
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _updateOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
    }
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var size = renderBox.size;
    var offset = renderBox.localToGlobal(Offset.zero);

    return OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        top: offset.dy + size.height,
        width: size.width,
        child: Material(
          elevation: 4,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            constraints: BoxConstraints(maxHeight: 200),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: _filteredProducts.isEmpty
                ? Padding(
                    padding: EdgeInsets.all(16),
                    child: Text(
                      'Aucun produit trouvé',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  )
                : ListView.builder(
                    shrinkWrap: true,
                    itemCount: _filteredProducts.length,
                    itemBuilder: (context, index) {
                      final product = _filteredProducts[index];
                      return InkWell(
                        onTap: () {
                          _selectProduct(product);
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: index < _filteredProducts.length - 1
                                    ? Colors.grey[200]!
                                    : Colors.transparent,
                                width: 1,
                              ),
                            ),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      product.nom,
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    Text(
                                      'Code: ${product.code}',
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.grey[600],
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                              Text(
                                '${product.prix} DT',
                                style: TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF3B82F6),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ),
      ),
    );
  }

  void _selectProduct(Produit product) {
    setState(() {
      _searchController.text = product.nom;
    });
    widget.onProductSelected(product);
    _hideSearchDropdown();
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _searchController,
      focusNode: _focusNode,
      decoration: InputDecoration(
        hintText: 'Rechercher un produit...',
        hintStyle: TextStyle(fontSize: 12, color: Colors.grey[500]),
        prefixIcon: Icon(Icons.search, size: 16, color: Colors.grey[600]),
        suffixIcon: widget.selectedProduct != null
            ? IconButton(
                icon: Icon(Icons.clear, size: 16),
                onPressed: () {
                  setState(() {
                    _searchController.clear();
                  });
                  widget.onProductSelected(null);
                  _filterProducts('');
                },
                constraints: BoxConstraints(minWidth: 24, minHeight: 24),
                padding: EdgeInsets.all(4),
              )
            : null,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        isDense: true,
      ),
      style: TextStyle(fontSize: 12),
      onChanged: _filterProducts,
    );
  }
}
